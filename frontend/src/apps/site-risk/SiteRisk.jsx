import { useEffect, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import mailImage from '../../assets/img/illust_mail.png';
import errorImage from '../../assets/img/rank_alert.png';
import ErrorComponent from '../../common/components/Error';
import { getSiteRiskWorstStatus, SiteRiskContext } from '../../common/context/SiteRiskContext';
import useModal from '../../common/hooks/useModal';
import { useErrorMessages } from '../../common/messages/error';
import { normalizeLanguage } from '../../common/utils/languageUtils';
import ChatButton from '../chat/ChatButton';
import NdsContactForm from './form/NdsContactForm';
import SslContactForm from './form/SslContactForm';
import ExpiredOverview from './overview/ExpiredOverview';
import OverView from './overview/Overview';
import Result from './result/Result';

const MAIL_IMAGE = mailImage;
const ERROR_IMAGE = errorImage;

function SiteRisk() {
  const { t, i18n } = useTranslation('check_site-risk');
  const errorMessages = useErrorMessages();
  const [isLoading, setIsLoading] = useState(true);
  const [result, setResult] = useState(null);
  const [error, setError] = useState('');
  const [grecaptcha, setGrecaptcha] = useState(null);
  const [modalChildren, setModalChildren] = useState(<></>);
  const [isProcessing, setIsProcessing] = useState(false);

  const MODAL_TITLES = {
    nds: t('お申し込みを受け付けいたしました'),
    cloud: t('無料お試しを受け付けいたしました'),
    ssl: t('無料お試しを受け付けいたしました'),
    impersonation: t('無料見積を受け付けいたしました'),
  };

  const MODAL_MESSAGES = {
    nds: <Trans ns="check_site-risk" i18nKey="詳細のご案内をメールアドレスに<br />お送りいたします。" components={{ br: <br /> }} />,
    cloud: t('メールアドレスにご案内をお送りいたします。'),
    ssl: t('メールアドレスにご案内をお送りいたします。'),
    impersonation: t('メールアドレスにご案内をお送りいたします。'),
  };

  const { show, Modal } = useModal();

  const queryParams = new URLSearchParams(location.search);
  const code = queryParams.get('code');

  const isExpired
    = !result?.nds && !result?.cloud && !result?.ssl && !result?.impersonation;

  console.log('Debug isExpired:', {
    isExpired,
    hasNds: !!result?.nds,
    hasCloud: !!result?.cloud,
    hasSsl: !!result?.ssl,
    hasImpersonation: !!result?.impersonation,
    result: result
  });

  const createModalContent = (target) => {
    if (target === 'nds') {
      const rank = result?.nds?.summary?.rank || 'C';
      return (
        <NdsContactForm
          onSubmit={handleContactSubmit}
          rank={rank}
        />
      );
    }

    if (target === 'ssl') {
      return <SslContactForm onSubmit={handleContactSubmit} />;
    }

    return null;
  };

  const showModalContent = (content) => {
    setModalChildren(content);
    show();
  };

  const createResultModal = (image, title, message) => (
    <div className="c-modalBox">
      <div className="c-modalBox__img">
        <img src={image} alt="" />
      </div>
      <h2 className="c-modalBox__title">{title}</h2>
      <p className="c-modalBox__text">{message}</p>
    </div>
  );

  useEffect(() => {
    const { grecaptcha } = window;
    if (grecaptcha) {
      grecaptcha.ready(() => {
        setGrecaptcha(grecaptcha);
      });
    }
  }, []);

  useEffect(() => {
    if (code) {
      setIsLoading(true);
      (async () => {
        try {
          const response = await fetch(
            `${import.meta.env.VITE_API_HOST}/api/site-risk?code=${code}`,
          );

          if (response.ok) {
            const { result, status } = await response.json();
            if (status === 'success') {
              console.log('API Response:', result); // Debug log
              setResult(result);
            }
          } else {
            const { status, message } = await response.json();
            if (status === 'error') {
              throw new Error(message);
            }
          }
        } catch (err) {
          if (err.message === 'Code is expired') {
            setError(errorMessages.CODE_EXPIRED());
            return;
          }
          if (err.message === 'Invalid code') {
            setError(errorMessages.INVALID_CODE());
            return;
          }
          console.error(err);
          setError(errorMessages.NOT_FOUND());
        } finally {
          setIsLoading(false);
        }
      })();
    }
  }, [code]);

  const handleContactSubmit = async ({ target, telephone, fullname }) => {
    if (['nds', 'ssl'].includes(target)) {
      // setIsModalClosable(false);
      // setIsSslProcessing(true);
    } else {
      setIsProcessing(true);
    }
    let image, title, message;
    try {
      if (!grecaptcha) {
        throw new Error('Not fount grecaptcha');
      }

      const recaptchaToken = await grecaptcha.execute(
        import.meta.env.VITE_RECAPTCHA_SITE_KEY,
        { action: 'submit' },
      );

      const supportedLang = normalizeLanguage(i18n.language);

      const response = await fetch(
        `${import.meta.env.VITE_API_HOST}/api/contact`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            code,
            email: result.overview?.email,
            targets: [target],
            recaptchaToken,
            threadTs: result.overview?.threadTs,
            fullname,
            telephone,
            lang: supportedLang,
          }),
        },
      );

      if (response.ok) {
        image = MAIL_IMAGE;
        title = MODAL_TITLES[target];
        message = MODAL_MESSAGES[target];
      } else {
        const { message } = await response.json();
        throw new Error(`Contact api error message: ${message}`);
      }
    } catch (err) {
      image = ERROR_IMAGE;
      title = t('エラーが発生しました。');
      message = t('再度お試しください。');

      console.error(err);
    } finally {
      if (['nds', 'ssl'].includes(target)) {
        // setIsSslProcessing(false);
        // setIsModalClosable(true);
      } else {
        setIsProcessing(false);
      }
      const modalContent = createResultModal(image, title, message);
      showModalContent(modalContent);
    }
  };

  const onClickedContact = (target) => {
    if (['nds', 'ssl'].includes(target)) {
      const modalContent = createModalContent(target);
      showModalContent(modalContent);
      return;
    }

    handleContactSubmit({ target });
  };

  if (isLoading) {
    return <></>;
  }

  if (error) {
    return <ErrorComponent text={error} />;
  }

  const worstStatus = getSiteRiskWorstStatus({ ...result.overview });

  return (
    <section>
      <div className="p-base">
        <SiteRiskContext.Provider value={{ worstStatus }}>
          <div className="p-base__inner">
            <div className="p-base__message">
              <div className="c-glassPanel">
                <p className="c-glassPanel__text">
                  <Trans
                    ns="check_site-risk"
                    i18nKey="Webサイトリスク診断日：{{date}}"
                    values={{ date: new Date(result.overview.createdAt).toLocaleString(i18n.language === 'en' ? 'en-US' : 'ja-JP') }}
                  />
                </p>
              </div>
            </div>
            {isExpired
              ? (
                <ExpiredOverview {...result.overview} code={code} result={result} />
              )
              : (
                <OverView {...result.overview} code={code} result={result} />
              )}
            {!isExpired && (
              <>
                <Modal>{modalChildren}</Modal>
                <Result
                  nds={result.nds}
                  cloud={result.cloud}
                  ssl={result.ssl}
                  impersonation={result.impersonation}
                  onClickedContact={onClickedContact}
                  isProcessing={isProcessing}
                />
              </>
            )}
          </div>
        </SiteRiskContext.Provider>
      </div>
      <div className="p-baseFixed">
        <div className="p-baseFixed__chatButton">
          <ChatButton />
        </div>
      </div>
    </section>
  );
}

export default SiteRisk;
