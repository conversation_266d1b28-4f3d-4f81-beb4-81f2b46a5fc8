import PropTypes from 'prop-types';
import { Fragment, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { ToastContainer, toast } from 'react-toastify';
import DropDownList from '../../common/components/DropDownList';
import InfoTooltip from '../../common/components/InfoTolltip';
import Snackbar from '../../common/components/Snackbar';
import ToggleSwitch from '../../common/components/ToggleSwitch';

var apiQueue = [];
const callApi = async (
  resolve,
  reject,
  code,
  isRegularly,
  interval,
  isNotification,
) => {
  apiQueue.push({
    code: code,
    isRegularly: isRegularly,
    interval: interval,
    isNotification: isNotification,
    resolve: resolve,
    reject: reject,
  });
  if (apiQueue.length > 1) {
    return;
  }
  while (apiQueue.length > 0) {
    const data = apiQueue[0];
    await _callApi(
      data.code,
      data.isRegularly,
      data.interval,
      data.isNotification,
    )
      .then(() => data.resolve())
      .catch(e => data.reject(e));
    apiQueue.shift();
  }
};

const _callApi = async (code, isRegularly, interval, isNotification) => {
  const response = await fetch(
    `${import.meta.env.VITE_API_HOST}/api/configuration`,
    {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ code, isRegularly, interval, isNotification }),
    },
  );

  if (!response.ok) {
    const { status, message } = await response.json();
    throw new Error(`Email api error ${status} message: ${message}`);
  }
};

const SiteRiskPeriodicCheckup = ({
  code,
  isRegularly,
  interval = 1,
  isNotification,
  nextCheckedAt,
  createdAt,
}) => {
  const { t, i18n } = useTranslation('check_site-risk');
  const [enablePeriodicCheckup, setEnablePeriodicCheckup] = useState(
    isRegularly ?? false,
  );
  const enableNewsDelivery = isNotification ?? false;

  const dateFormatLocale = i18n.language === 'en' ? 'en-US' : 'sv-SE';

  const [checkInterval, setCheckInterval] = useState(`${t('{{count}}ヶ月毎', { count: interval })}`);

  const [nextCheckDateStr, setNextCheckDateStr] = useState(
    (new Date('2025-03-10').getTime() < new Date(nextCheckedAt).getTime()
      ? new Date(nextCheckedAt)
      : new Date('2025-03-10')
    ).toLocaleDateString(dateFormatLocale),
  );

  const updateNextCheckDate = (itv) => {
    if (createdAt) {
      const newDate = new Date(createdAt);
      newDate.setMonth(newDate.getMonth() + itv);
      setNextCheckDateStr(
        (new Date('2025-03-10').getTime() < newDate.getTime()
          ? newDate
          : new Date('2025-03-10')
        ).toLocaleDateString(dateFormatLocale),
      );
    }
  };

  const handleTogglePeriodicCheckup = () => {
    const itv = parseInt(checkInterval[0]);
    const p = new Promise((resolve, reject) => {
      callApi(
        resolve,
        reject,
        code,
        !enablePeriodicCheckup,
        itv,
        enableNewsDelivery,
      );
    });
    p.then(() => {
      toast(<Snackbar>{t('設定が保存されました')}</Snackbar>);
    });
    setEnablePeriodicCheckup(!enablePeriodicCheckup);
    updateNextCheckDate(parseInt(checkInterval[0]));
  };

  const onChangeInterval = (data) => {
    const itv = parseInt(data[0]);
    const p = new Promise((resolve, reject) => {
      callApi(
        resolve,
        reject,
        code,
        enablePeriodicCheckup,
        itv,
        enableNewsDelivery,
      );
    });
    p.then(() => {
      toast(<Snackbar>{t('設定が保存されました')}</Snackbar>);
    });
    setCheckInterval(data);
    updateNextCheckDate(itv);
  };

  const nextCheckDateDisplay = enablePeriodicCheckup ? nextCheckDateStr : null;

  return (
    <Fragment>
      <ToastContainer hideProgressBar={true} limit={3} />
      <div className="c-siteRiskPeriodicCheckup">
        <div className="c-siteRiskPeriodicCheckup__textWrap">
          <div className="c-siteRiskPeriodicCheckup__icon">
            <span className="icon-base icon-sec-cycle icon-size16 icon-color-darkGreen" />
          </div>
          <p className="c-siteRiskPeriodicCheckup__text">
            {t('定期診断を無償で行い、メールでお知らせします。')}
          </p>
        </div>
        <div className="c-siteRiskPeriodicCheckup__list">
          <div className="c-siteRiskPeriodicCheckupElement">
            <span className="c-siteRiskPeriodicCheckupElement__label">{t('定期診断')}</span>
            <div className="c-siteRiskPeriodicCheckupElement__toggle">
              <ToggleSwitch
                name="site_risk_periodic_checkup"
                isOn={enablePeriodicCheckup}
                handleToggle={handleTogglePeriodicCheckup}
              />
            </div>
            <div className="c-siteRiskPeriodicCheckupElement__select">
              <DropDownList
                name="site_risk_periodic_dropdown"
                value={checkInterval}
                onChange={onChangeInterval}
                disabled={!enablePeriodicCheckup}
                options={[t('{{count}}ヶ月毎', { count: 1 }), t('{{count}}ヶ月毎', { count: 3 }), t('{{count}}ヶ月毎', { count: 6 })]}
              />
            </div>
            <div className="c-siteRiskPeriodicCheckupElement__tooltip">
              <InfoTooltip
                content={(
                  <Trans
                    ns="check_site-risk"
                    i18nKey="以下2点より、Webサイトを常に安全に保つためには月1回の診断を推奨します。<br />①2024年には「1日あたり100個以上※1」の新規脆弱性が確認されていること<br />②システム改修/更新により新たな脆弱性が発生する可能性があること<br />※1 出典：https://www.cvedetails.com/browse-by-date.php"
                    components={{ br: <br /> }}
                  />
                )}
              />
            </div>
          </div>
          {enablePeriodicCheckup && (
            <div className="c-siteRiskPeriodicCheckup__date">
              {t('次回診断日：{{date}}', { date: nextCheckDateDisplay })}
            </div>
          )}
        </div>
      </div>
    </Fragment>
  );
};

SiteRiskPeriodicCheckup.propTypes = {
  code: PropTypes.string,
  isRegularly: PropTypes.bool,
  interval: PropTypes.number,
  isNotification: PropTypes.bool,
  nextCheckedAt: PropTypes.string,
  createdAt: PropTypes.string,
};

export default SiteRiskPeriodicCheckup;
