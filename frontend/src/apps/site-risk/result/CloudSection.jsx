import PropTypes from 'prop-types';
import { useContext } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import cloudImageCloud from '../../../assets/img/rank_cloud.png';
import cloudImageWarning from '../../../assets/img/rank_warning.png';
import Button from '../../../common/components/Button';
import SnsShareContainer from '../../../common/components/SnsShareContainer';
import { SiteRiskContext } from '../../../common/context/SiteRiskContext';
import { formatDatetime } from '../../../common/utils';
import { generateSiteRiskShareContent } from '../../../common/utils/shareUtils';
import NotFoundSection from './NotFoundSection';
import ProcessingSection from './ProcessingSection';

const CLOUD_IMAGE_CLOUD = cloudImageCloud;
const CLOUD_IMAGE_WARNING = cloudImageWarning;

function CloudSection({ cloud }) {
  const { t, i18n } = useTranslation('check_site-risk');
  const { worstStatus } = useContext(SiteRiskContext);
  const shareText = generateSiteRiskShareContent(worstStatus);

  if (!cloud) {
    return (
      <section>
        <div className="c-panel gtm-view c-panel--tab">
          <h2 className="c-panel__title">
            <span className="icon-base icon-sec-cloud icon-size40 icon-color-darkGreen" />
            {t('クラウド利用・リスク診断')}
          </h2>
          <ProcessingSection />
        </div>
      </section>
    );
  }

  if (cloud && cloud.status === 'error') {
    return (
      <section>
        <div className="c-panel gtm-view c-panel--tab">
          <h2 className="c-panel__title">
            <span className="icon-base icon-sec-cloud icon-size40 icon-color-darkGreen" />
            {t('クラウド利用・リスク診断')}
          </h2>
          <NotFoundSection />
        </div>
      </section>
    );
  }

  return (
    <section>
      <div id="section_cloud" className="c-panel gtm-view c-panel--tab">
        <h2 className="c-panel__title">
          <span className="icon-base icon-sec-cloud icon-size40 icon-color-darkGreen" />
          {t('クラウド利用・リスク診断')}
        </h2>
        <div className="c-panel__content">
          <div className="c-panel__side">
            <div className="c-panel__rankImg">
              {cloud.summary.isUsed
                ? (
                  <img src={CLOUD_IMAGE_WARNING} alt="warning" />
                )
                : (
                  <img src={CLOUD_IMAGE_CLOUD} alt="cloud" />
                )}
            </div>
            {/* pcのみ表示 */}
            <div className="c-panel__sideButton c-panel__sideButton--spNone">
              {cloud.summary.isUsed && (
                <Button
                  id="contact_cloud"
                  as="a"
                  href={`http://cloud.shisho.dev/hello/cloud?utm_source=gmo_netsec_lp&utm_medium=gmo_jp&utm_campaign=gmo_netsec&utm_content=${cloud.details[0]?.cloud}`}
                  target="_blank"
                  rel="noopener"
                  variant="accent"
                  widthSize="full"
                  referrerPolicy="strict-origin-when-cross-origin"
                >
                  <Trans ns="check_site-risk" i18nKey="クラウドの中身も診断<br />(無料お試し)" components={{ br: <br /> }} />
                </Button>
              )}
            </div>
            <div className="c-panel__snsShare c-panel__snsShare--spNone">
              <SnsShareContainer
                id="cloud_sns"
                text={shareText}
                isShowFacebook={false}
                isShowInstagram={false}
                isShowYouTube={false}
                isShowTikTok={false}
              />
            </div>
            {/* pcのみ表示 end */}
          </div>
          <div className="c-panel__main">
            {cloud.summary.isUsed && (
              <p className="c-panel__note c-panel__note--warning">{t('要確認')}</p>
            )}
            {cloud.details && cloud.details.length > 0 && (
              <div className="c-panel__result">
                {cloud.details.map((c, idx) => (
                  <p key={idx}>
                    <Trans
                      ns="check_site-risk"
                      i18nKey="{{cloud}}のクラウドをご利用中です。クラウド環境のセキュリティ障害の99%<note>※</note>が、お客様の設定ミスなどに起因するようになるといわれています。設定内容をご確認ください。<br /><note>※ Gartner社レポート「Is the Cloud Secure?」より</note>"
                      values={{ cloud: c.cloud }}
                      components={{ note: <span className="c-panel__result c-panel__result--note" />, br: <br /> }}
                    />
                  </p>
                ))}
              </div>
            )}
            {/* cloud利用なし */}
            {!cloud.summary.isUsed && (
              <div className="c-panelList__noteWrap c-panelList__noteWrap--center">
                <p className="c-panelList__note">
                  <span className="icon-base icon-sec-attention icon-size16 icon-color-darkGreen" />
                  {t('AWS・Google Cloud・Microsoft Azureは使用していません')}
                </p>
              </div>
            )}
            {/* spのみ表示 */}
            <div className="c-panel__sideButton c-panel__sideButton--pcNone">
              {cloud.summary.isUsed && (
                <Button
                  id="contact_cloud_sp"
                  as="a"
                  href={`http://cloud.shisho.dev/hello/cloud?utm_source=gmo_netsec_lp&utm_medium=gmo_jp&utm_campaign=gmo_netsec&utm_content=${cloud.details[0]?.cloud}`}
                  target="_blank"
                  rel="noopener"
                  variant="accent"
                  widthSize="full"
                  referrerPolicy="strict-origin-when-cross-origin"
                >
                  <Trans ns="check_site-risk" i18nKey="クラウドの中身も診断<br />(無料お試し)" components={{ br: <br /> }} />
                </Button>
              )}
            </div>
            {/* spのみ表示 end */}
            {/* cloud利用あり */}
            {cloud.summary.isUsed && (
              <div className="p-siteRiskToggle">
                <div className="p-siteRiskAccordionList">
                  <details className="p-siteRiskAccordion">
                    <summary
                      id="accordion_cloud"
                      className="p-siteRiskAccordion__head"
                    >
                      <h3 id="accordion_cloud_h3">
                        <span className="icon-base icon-sec-attention icon-size20 icon-color-orange" />
                        {t('あなたのクラウド、本当に安全ですか？')}
                      </h3>
                    </summary>
                    <div className="p-siteRiskAccordion__body">
                      <dl className="p-siteRiskDl">
                        <dt>
                          {t('・想定される影響')}
                          <span className="p-siteRiskDl__text--em">
                            {t('― 実は大事なクラウドリスク')}
                          </span>
                        </dt>
                        <dd>
                          {t('クラウドを利用していると、サービスの設定の間違いによって、本来は外部に公開するはずのないデータが見えてしまうことがあります。たとえば、「社内だけで使っているはずのデータが、インターネット上に公開されていた」などが典型的なケースです。クラウドだから安全、というわけではありません。')}
                        </dd>
                        <dd>
                          <Trans
                            ns="check_site-risk"
                            i18nKey="また、<btn>Googleの調査「H1 2025 Threat Horizons Report」</btn>でも、こうした設定ミスを狙う攻撃が、2024年末にかけて増えていく見込みであると報告されています。今後ますます注意が必要です。"
                            components={{
                              btn: (
                                <Button
                                  id="cloud_google_report_link"
                                  as="a"
                                  href="https://services.google.com/fh/files/misc/threat_horizons_report_h1_2025.pdf"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  variant="text"
                                />
                              ),
                            }}
                          />
                        </dd>
                      </dl>
                      <dl className="p-siteRiskDl">
                        <dt>{t('・対策方法')}</dt>
                        <dd>
                          {t('どんなデータやサーバーがクラウド上で動いているかを常に把握すること、およびクラウドのセキュリティベストプラクティスを学び、「自分たちのシステムは本当に大丈夫か」を定期的に確認することをおすすめいたします。')}
                          <br />
                          {t('「まずは、何がどこで動いているのか」を整理し、誤って公開されているものがないかチェックしましょう。万が一の設定ミスが大きなトラブルにつながるのを防ぐためにも、定期的な見直しがとても大切です。セルフチェックのために、自社システムを点検する際に役立つ資料はこちらです。')}
                        </dd>
                        {cloud.details[0].cloud === 'AWS' && (
                          <dd>
                            <ul className="p-siteRiskLink">
                              <li>
                                <Button
                                  id="cloud_aws_checklist"
                                  as="a"
                                  href="https://docs.aws.amazon.com/ja_jp/securityhub/latest/userguide/cis-aws-foundations-benchmark.html"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  external
                                  exIcon="small"
                                  variant="text"
                                >
                                  {t('- AWS セキュリティのチェックリスト（CIS AWS Foundations Benchmark）')}
                                </Button>
                              </li>
                              <li>
                                <Button
                                  id="cloud_aws_column"
                                  as="a"
                                  href="https://blog.flatt.tech/archive/category/AWS"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  external
                                  exIcon="small"
                                  variant="text"
                                >
                                  {t('- AWS クラウドセキュリティに関するコラム')}
                                </Button>
                              </li>
                              <li>
                                <Button
                                  id="cloud_aws_best_practice"
                                  as="a"
                                  href="https://shisho.dev/ja/methods/aws"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  external
                                  exIcon="small"
                                  variant="text"
                                >
                                  {t('- AWS クラウドベストプラクティスの解説')}
                                </Button>
                              </li>
                            </ul>
                          </dd>
                        )}
                        {cloud.details[0].cloud === 'GoogleCloud' && (
                          <dd>
                            <ul className="p-siteRiskLink">
                              <li>
                                <Button
                                  id="cloud_google_checklist"
                                  as="a"
                                  href="https://cloud.google.com/security-command-center/docs/security-posture-overview?hl=ja"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  external
                                  exIcon="small"
                                  variant="text"
                                >
                                  {t('- Google Cloud セキュリティのチェックリスト')}
                                </Button>
                              </li>
                            </ul>
                          </dd>
                        )}
                        {cloud.details[0].cloud === 'Azure' && (
                          <dd>
                            <ul className="p-siteRiskLink">
                              <li>
                                <Button
                                  id="cloud_azure_checklist"
                                  as="a"
                                  href="https://learn.microsoft.com/ja-jp/security/benchmark/azure/overview"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  external
                                  exIcon="small"
                                  variant="text"
                                >
                                  {t('- Azure セキュリティのチェックリスト')}
                                </Button>
                              </li>
                            </ul>
                          </dd>
                        )}
                        <dd>
                          {t('もし専門家に相談したい場合や、設定ミスを総合的にチェックしたい場合はお気軽に「クラウドの中身も診断」より無料でお試しください。')}
                        </dd>
                      </dl>
                    </div>
                  </details>
                </div>
              </div>
            )}
          </div>
        </div>
        {cloud.summary.createdAt && (
          <p className="c-panel__annotation c-panel__annotation--xs">
            <Trans
              ns="check_site-risk"
              i18nKey="本診断結果は、{{date}}時点のものです。診断結果は、<ag>利用規約</ag>を参照してください。"
              values={{
                date: i18n.language === 'en'
                  ? new Date(cloud.summary.createdAt).toLocaleString('en-US')
                  : formatDatetime(cloud.summary.createdAt),
              }}
              components={{
                ag: (
                  <Button
                    as="a"
                    href="https://www.gmo.jp/security/check/agreement/"
                    target="_blank"
                    rel="noopener"
                    variant="textXs"
                    referrerPolicy="strict-origin-when-cross-origin"
                  />
                ),
              }}
            />
          </p>
        )}
        {/* spのみ表示 SNS */}
        <div className="c-panel__snsShare c-panel__snsShare--pcNone">
          <SnsShareContainer
            id="cloud_sns_sp"
            text={shareText}
            isShowFacebook={false}
            isShowInstagram={false}
            isShowYouTube={false}
            isShowTikTok={false}
          />
        </div>
        {/* spのみ表示 SNS end */}
      </div>
    </section>
  );
}

CloudSection.propTypes = {
  cloud: PropTypes.shape({
    status: PropTypes.string,
    summary: PropTypes.shape({
      isUsed: PropTypes.bool,
      createdAt: PropTypes.string,
    }),
    details: PropTypes.arrayOf(PropTypes.shape({ cloud: PropTypes.string })),
  }),
};

export default CloudSection;
