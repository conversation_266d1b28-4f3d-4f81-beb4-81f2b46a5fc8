import PropTypes from 'prop-types';
import { useContext, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import Button from '../../../common/components/Button';
import RankImage from '../../../common/components/RankImage';
import SnsShareContainer from '../../../common/components/SnsShareContainer';
import { SiteRiskContext } from '../../../common/context/SiteRiskContext';
import { formatDatetime } from '../../../common/utils';
import { generateSiteRiskShareContent } from '../../../common/utils/shareUtils';
import Badge from './Badge';
import NotFoundSection from './NotFoundSection';
import ProcessingSection from './ProcessingSection';

function NdsSection({ nds, onClickedContact, isProcessing = false }) {
  const { t, i18n } = useTranslation('check_site-risk');
  const [isAllShown, setIsAllShown] = useState(false);
  const { worstStatus } = useContext(SiteRiskContext);
  const shareText = generateSiteRiskShareContent(worstStatus);

  const LEVELS = {
    critical: { message: t('緊急'), status: 'alert' },
    high: { message: t('高リスク'), status: 'high' },
    medium: { message: t('中リスク'), status: 'warning' },
    low: { message: t('低リスク'), status: 'low' },
    info: { message: t('情報'), status: 'info' },
  };

  const RANKS = {
    A: t('問題となる異常は見つかりませんでしたが、より詳細にセキュリティ状態をチェックできる詳細診断モードをお勧めします。'),
    B: t('問題となる異常は見つかりませんでしたが、より詳細にセキュリティ状態をチェックできる詳細診断モードをお勧めします。'),
    C: t('セキュリティに欠陥があり、サイバー攻撃の標的になりやすい状態です。まずは緊急度の高いリスク項目から、セキュリティ対策を実施してください。'),
    D: t('セキュリティに欠陥があり、サイバー攻撃の標的になりやすい状態です。まずは緊急度の高いリスク項目から、セキュリティ対策を実施してください。'),
    E: t('重大かつ緊急性の高いセキュリティの欠陥があり、サイバー攻撃の標的になりやすい状態です。まずは緊急度の高いリスク項目から、セキュリティ対策を実施してください。'),
  };

  const RANKSNOTE = {
    A: t('安全です'),
    B: t('安全です'),
    C: t('要対策'),
    D: t('要対策'),
    E: t('要緊急対応'),
  };

  if (!nds) {
    return (
      <section>
        <div className="c-panel gtm-view c-panel--tab">
          <h2 className="c-panel__title">
            <span className="icon-base icon-sec-security icon-size40 icon-color-darkGreen" />
            {t('Webサイト脆弱性診断')}
          </h2>
          <ProcessingSection />
        </div>
      </section>
    );
  }

  if (nds.status && nds.status === 'error') {
    return (
      <section>
        <div className="c-panel gtm-view c-panel--tab">
          <h2 className="c-panel__title">
            <span className="icon-base icon-sec-security icon-size40 icon-color-darkGreen" />
            {t('Webサイト脆弱性診断')}
          </h2>
          <NotFoundSection />
        </div>
      </section>
    );
  }

  const defaultVisibleCount = 4;

  const visibleDetails = isAllShown
    ? nds.details
    : nds.details.slice(0, defaultVisibleCount);

  return (
    <section>
      <div id="section_nds" className="c-panel gtm-view c-panel--tab">
        <h2 className="c-panel__title">
          <span className="icon-base icon-sec-security icon-size40 icon-color-darkGreen" />
          {t('Webサイト脆弱性診断')}
        </h2>
        <div className="c-panel__content">
          <div className="c-panel__side">
            <div className="c-panel__rankImg">
              <RankImage rank={nds.summary.rank} type="nds" />
            </div>
            {/* pcのみ表示 */}
            <div className="c-panel__sideButton c-panel__sideButton--spNone">
              <Button
                id="nds_link_detail"
                as="a"
                href="https://id.gmo-cybersecurity.com/register-security24"
                target="_blank"
                rel="noopener"
                variant="accent"
                widthSize="full"
                referrerPolicy="strict-origin-when-cross-origin"
              >
                {t('詳細に診断する')}
              </Button>
              {(nds.summary.rank === 'C' || nds.summary.rank === 'D' || nds.summary.rank === 'E') && (
                <Button
                  id="contact_nds"
                  onClick={onClickedContact}
                  variant="accentTertiary"
                  widthSize="full"
                  disabled={isProcessing}
                >
                  {t('対策する(無料見積)')}
                </Button>
              )}
            </div>
            <div className="c-panel__snsShare c-panel__snsShare--spNone">
              <SnsShareContainer
                id="nds_sns"
                text={shareText}
                isShowFacebook={false}
                isShowInstagram={false}
                isShowYouTube={false}
                isShowTikTok={false}
              />
            </div>
            {/* pcのみ表示 end */}
          </div>
          <div className="c-panel__main">
            <p className={`c-panel__note c-panel__note--${nds.summary.rank}`}>
              {RANKSNOTE[nds.summary.rank]}
            </p>
            <p className="c-panel__result">{RANKS[nds.summary.rank]}</p>
            {/* spのみ表示 */}
            <div className="c-panel__sideButton c-panel__sideButton--pcNone">
              <Button
                id="nds_link_detail_sp_upper"
                as="a"
                href="https://id.gmo-cybersecurity.com/register-security24"
                target="_blank"
                rel="noopener"
                variant="accent"
                widthSize="full"
                referrerPolicy="strict-origin-when-cross-origin"
              >
                {t('詳細に診断する')}
              </Button>
              {(nds.summary.rank === 'C' || nds.summary.rank === 'D' || nds.summary.rank === 'E') && (
                <Button
                  id="contact_nds_sp_upper"
                  onClick={onClickedContact}
                  variant="accentTertiary"
                  widthSize="full"
                  disabled={isProcessing}
                >
                  {t('対策する(無料見積)')}
                </Button>
              )}
            </div>
            {/* spのみ表示 end */}
            <ul className="p-siteRiskSummary">
              <li className="p-siteRiskSummary__list">
                <span
                  className={
                    nds.summary.levelCounts.critical > 0
                      ? 'p-siteRiskSummary__count p-siteRiskSummary__count--error'
                      : 'p-siteRiskSummary__count'
                  }
                >
                  {nds.summary.levelCounts.critical}
                </span>
                <p className="p-siteRiskSummary__text">{t('緊急')}</p>
              </li>
              <li className="p-siteRiskSummary__list">
                <span
                  className={
                    nds.summary.levelCounts.high > 0
                      ? 'p-siteRiskSummary__count p-siteRiskSummary__count--error'
                      : 'p-siteRiskSummary__count'
                  }
                >
                  {nds.summary.levelCounts.high}
                </span>
                <p className="p-siteRiskSummary__text">{t('高リスク')}</p>
              </li>
              <li className="p-siteRiskSummary__list">
                <span
                  className={
                    nds.summary.levelCounts.medium > 0
                      ? 'p-siteRiskSummary__count p-siteRiskSummary__count--error'
                      : 'p-siteRiskSummary__count'
                  }
                >
                  {nds.summary.levelCounts.medium}
                </span>
                <p className="p-siteRiskSummary__text">{t('中リスク')}</p>
              </li>
              <li className="p-siteRiskSummary__list">
                <span
                  className={
                    nds.summary.levelCounts.low > 0
                      ? 'p-siteRiskSummary__count p-siteRiskSummary__count--error'
                      : 'p-siteRiskSummary__count'
                  }
                >
                  {nds.summary.levelCounts.low}
                </span>
                <p className="p-siteRiskSummary__text">{t('低リスク')}</p>
              </li>
              <li className="p-siteRiskSummary__list">
                <span className="p-siteRiskSummary__count">
                  {nds.summary.levelCounts.info}
                </span>
                <p className="p-siteRiskSummary__text">{t('情報')}</p>
              </li>
            </ul>
            <div className="p-siteRiskToggle">
              <ul
                className={
                  isAllShown
                    ? 'p-siteRiskAccordionList p-siteRiskAccordionList--all'
                    : 'p-siteRiskAccordionList p-siteRiskAccordionList--hiddden'
                }
              >
                {visibleDetails.map((detail, index) => (
                  <li key={index}>
                    <details className="p-siteRiskAccordion">
                      <summary
                        id={`accordion_nds_${detail.level}_${index}_summary`}
                        className="p-siteRiskAccordion__head"
                      >
                        <h3 id={`accordion_nds_${detail.level}_${index}_h3`}>
                          {detail.title}
                        </h3>
                        <Badge
                          status={LEVELS[detail.level].status}
                          message={LEVELS[detail.level].message}
                        />
                      </summary>
                      <div className="p-siteRiskAccordion__body">
                        {detail.impact && (
                          <dl className="p-siteRiskDl">
                            <dt>{t('・想定される影響')}</dt>
                            <dd>{detail.impact}</dd>
                          </dl>
                        )}
                        {detail.measure && (
                          <dl className="p-siteRiskDl">
                            <dt>{t('・対策方法')}</dt>
                            <dd>{detail.measure}</dd>
                          </dl>
                        )}
                      </div>
                    </details>
                  </li>
                ))}
              </ul>
              {nds.details.length > defaultVisibleCount && (
                <div className="p-siteRiskToggle__button">
                  <Button
                    id={`nds_details_more_${!isAllShown}`}
                    onClick={() => setIsAllShown(!isAllShown)}
                    variant="text"
                    widthSize="full"
                  >
                    {isAllShown
                      ? (
                        <>
                          {t('閉じる')}
                          <span className="p-siteRiskToggle__arrowTop" />
                        </>
                      )
                      : (
                        <>
                          {t('もっと見る')}
                          <span className="p-siteRiskToggle__arrowBottom" />
                        </>
                      )}
                  </Button>
                </div>
              )}
            </div>
            <div className="p-siteRiskCardGray">
              <p className="p-siteRiskCardGray__text">
                <Trans
                  ns="check_site-risk"
                  i18nKey="本脆弱性診断では、診断対象サイトに影響を与えない範囲で<about>「ネットワーク診断・Webアプリケーション診断・CMS診断」</about>を実施しています。診断結果が「安全です」の場合、「一定の不正アクセス対策」が講じられ、一定の安全性が確保されていることを示します。「より正確な結果」「検知項目に対する対策」をお求めの場合は、以下「対策する(無料見積)」より<contact>お問い合わせください。</contact>"
                  components={{
                    about: (
                      <Button
                        id="nds_link"
                        as="a"
                        href="https://gmo-cybersecurity.com/column/assessment/about/"
                        target="_blank"
                        rel="noopener"
                        variant="textInline"
                        referrerPolicy="strict-origin-when-cross-origin"
                      />
                    ),
                    contact: (
                      <Button
                        id="contact_nds_text"
                        onClick={onClickedContact}
                        variant="textInline"
                        disabled={isProcessing}
                      />
                    ),
                  }}
                />
              </p>
            </div>
            {/* spのみ表示 */}
            <div className="c-panel__sideButton c-panel__sideButton--bottom c-panel__sideButton--pcNone">
              <Button
                id="nds_link_detail_sp"
                as="a"
                href="https://id.gmo-cybersecurity.com/register-security24"
                target="_blank"
                rel="noopener"
                variant="accent"
                widthSize="full"
                referrerPolicy="strict-origin-when-cross-origin"
              >
                {t('詳細に診断する')}
              </Button>
              {(nds.summary.rank === 'C' || nds.summary.rank === 'D' || nds.summary.rank === 'E') && (
                <Button
                  id="contact_nds_sp"
                  onClick={onClickedContact}
                  variant="accentTertiary"
                  widthSize="full"
                  disabled={isProcessing}
                >
                  {t('対策する(無料見積)')}
                </Button>
              )}
            </div>
            {/* spのみ表示 end */}
          </div>
        </div>
        {nds.summary.assessmentTime && (
          <p className="c-panel__annotation c-panel__annotation--xs">
            <Trans
              ns="check_site-risk"
              i18nKey="本診断結果は、{{date}}時点のものです。診断結果は、<btn>利用規約</btn>をご確認のうえ、最終的にはユーザーの皆様のご判断においてご利用ください。"
              values={{
                date: i18n.language === 'en'
                  ? new Date(nds.summary.assessmentTime).toLocaleString('en-US')
                  : formatDatetime(nds.summary.assessmentTime),
              }}
              components={{
                btn: (
                  <Button
                    as="a"
                    href="https://www.gmo.jp/security/check/agreement/"
                    target="_blank"
                    rel="noopener"
                    variant="textXs"
                    referrerPolicy="strict-origin-when-cross-origin"
                  />
                ),
              }}
            />
          </p>
        )}
        {/* spのみ表示 SNS */}
        <div className="c-panel__snsShare c-panel__snsShare--pcNone">
          <SnsShareContainer
            id="nds_sns_sp"
            text={shareText}
            isShowFacebook={false}
            isShowInstagram={false}
            isShowYouTube={false}
            isShowTikTok={false}
          />
        </div>
        {/* spのみ表示 SNS end */}
      </div>
    </section>
  );
}

NdsSection.propTypes = {
  nds: PropTypes.shape({
    status: PropTypes.string,
    details: PropTypes.array,
    summary: PropTypes.shape({
      rank: PropTypes.string,
      levelCounts: PropTypes.shape({
        critical: PropTypes.number,
        high: PropTypes.number,
        medium: PropTypes.number,
        low: PropTypes.number,
        info: PropTypes.number,
      }),
      assessmentTime: PropTypes.string,
    }),
  }),
  onClickedContact: PropTypes.func.isRequired,
  isProcessing: PropTypes.bool,
};

export default NdsSection;
