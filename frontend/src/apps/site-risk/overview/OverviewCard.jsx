import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { useMediaQuery } from 'react-responsive';
import overviewImageAlert from '../../../assets/img/rank_alert.png';
import overviewImageCloud from '../../../assets/img/rank_cloud.png';
import overviewImageSafe from '../../../assets/img/rank_safe.png';
import overviewImageWarning from '../../../assets/img/rank_warning.png';
import RankImage from '../../../common/components/RankImage';

const OVERVIEW_IMAGE_SAFE = overviewImageSafe;
const OVERVIEW_IMAGE_WARNING = overviewImageWarning;
const OVERVIEW_IMAGE_ALERT = overviewImageAlert;
const OVERVIEW_IMAGE_CLOUD = overviewImageCloud;

function RankCard({ type, rank, text }) {
  if (type === 'nds') {
    return (
      <>
        <div className="c-overviewCard__detail">
          <div className="c-overviewCard__icon">
            <RankImage rank={rank} type="nds" />
          </div>
          <div className="c-overviewCard__text">
            <span
              className={`c-overviewCard__badge c-overviewCard__badge--${rank}
    `}
            >
              {text}
            </span>
          </div>
        </div>
      </>
    );
  }
  return (
    <>
      <div className="c-overviewCard__detail">
        <div className="c-overviewCard__icon">
          <RankImage rank={rank} type="impersonation" />
        </div>
        <div className="c-overviewCard__text">
          <span
            className={`c-overviewCard__badge c-overviewCard__badge--imp${rank}
    `}
          >
            {text}
          </span>
        </div>
      </div>
    </>
  );
}

function OverviewCard({
  id,
  className,
  icon: Icon,
  title,
  status,
  rank,
  text,
  onClick,
  type,
}) {
  const { t } = useTranslation('check_site-risk');
  const isMobile = useMediaQuery({ query: '(max-width: 600px)' });
  return (
    <button id={id} onClick={onClick} className={className}>
      <div className="c-overviewCard__title">
        {Icon && <Icon />}
        {title}
      </div>
      {status === 'processing' && (
        <>
          <div className="c-overviewCard__detail">
            <div className="c-overviewCard__icon">
              <div className="c-loader" />
            </div>
            <div className="c-overviewCard__text">{t('診断実施中')}</div>
          </div>
        </>
      )}
      {status === 'rank' && <RankCard rank={rank} type={type} text={text} />}
      {status === 'safe' && (
        <>
          <div className="c-overviewCard__detail">
            <div className="c-overviewCard__icon">
              <img src={OVERVIEW_IMAGE_SAFE} alt="safe" />
            </div>
            <div className="c-overviewCard__text">
              <span className="c-overviewCard__badge">{text}</span>
            </div>
          </div>
        </>
      )}
      {status === 'warning' && (
        <>
          <div className="c-overviewCard__detail">
            <div className="c-overviewCard__icon">
              <img src={OVERVIEW_IMAGE_WARNING} alt="warning" />
            </div>
            <div className="c-overviewCard__text">
              <span className="c-overviewCard__badge c-overviewCard__badge--warning">
                {text}
              </span>
            </div>
          </div>
        </>
      )}
      {status === 'alert' && (
        <>
          <div className="c-overviewCard__detail">
            <div className="c-overviewCard__icon">
              <img src={OVERVIEW_IMAGE_ALERT} alt="alert" />
            </div>
            <div className="c-overviewCard__text">
              <span className="c-overviewCard__badge c-overviewCard__badge--alert">
                {text}
              </span>
            </div>
          </div>
        </>
      )}
      {status === 'error' && (
        <>
          <div className="c-overviewCard__detail">
            <div className="c-overviewCard__icon">
              {isMobile
                ? (
                  <span
                    className="icon-base icon-sec-error icon-color-darkGreen icon-size24"
                  />
                )
                : (
                  <span
                    className="icon-base icon-sec-error icon-color-darkGreen icon-size40"
                  />
                )}
            </div>
            <div className="c-overviewCard__text">
              {t('IPアドレスが確認できません')}
            </div>
          </div>
        </>
      )}
      {status === 'cloud' && (
        <>
          <div className="c-overviewCard__detail">
            <div className="c-overviewCard__icon">
              <img src={OVERVIEW_IMAGE_CLOUD} alt="cloud" />
            </div>
            <div className="c-overviewCard__text">{text}</div>
          </div>
        </>
      )}
    </button>
  );
}

RankCard.propTypes = {
  type: PropTypes.string.isRequired,
  rank: PropTypes.string.isRequired,
  text: PropTypes.string.isRequired,
};

OverviewCard.propTypes = {
  id: PropTypes.string,
  className: PropTypes.string,
  icon: PropTypes.elementType,
  title: PropTypes.oneOfType([PropTypes.string, PropTypes.node]).isRequired,
  status: PropTypes.string.isRequired,
  rank: PropTypes.string,
  text: PropTypes.string,
  onClick: PropTypes.func,
  type: PropTypes.string,
};

export default OverviewCard;
