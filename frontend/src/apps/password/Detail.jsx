import PropTypes from 'prop-types';
import { Trans, useTranslation } from 'react-i18next';

function Detail({ title, breachDate }) {
  const { i18n } = useTranslation('check_password');
  return (
    <div className="p-passwordDetail">
      <div className="p-passwordDetail__title">{title}</div>
      {/* TODO: 確認 漏洩した日 */}
      {breachDate && (
        <div className="p-passwordDetail__date">
          <Trans
            ns="check_password"
            i18nKey="漏洩した日：{{date}}"
            values={{ date: i18n.language === 'en' ? new Intl.DateTimeFormat('en-US').format(new Date(breachDate)) : breachDate }}
          />
        </div>
      )}
    </div>
  );
}

Detail.propTypes = {
  title: PropTypes.string.isRequired,
  breachDate: PropTypes.string,
};

export default Detail;
