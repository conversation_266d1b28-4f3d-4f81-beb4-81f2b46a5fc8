import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import contactImagePw from '../../assets/img/pw_btn.png';
import contactImagePwEn from '../../assets/img/pw_btn_en.png';
import contactImageWeb from '../../assets/img/web_btn.png';
import { normalizeLanguage } from '../utils/languageUtils';
import Button from './Button';

const CONTACT_IMAGE_PW = { ja: contactImagePw, en: contactImagePwEn };
const CONTACT_IMAGE_WEB = contactImageWeb;

const ContactButton = ({ isPasswordSite = false, onClick }) => {
  const { t, i18n } = useTranslation('common');
  const handleClick = () => {
    onClick();
    const contactSection = document.getElementById('anchorLink_contact');
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="p-baseContact">
      <Button onClick={handleClick} variant="image">
        {isPasswordSite
          ? (
            <img
              id="contact_common"
              src={CONTACT_IMAGE_PW[normalizeLanguage(i18n.language)]}
              alt={t('法人様向け専門家へ相談する')}
            />
          )
          : (
            <img
              id="contact_common"
              src={CONTACT_IMAGE_WEB}
              alt={t('専門家へ相談する')}
            />
          )}
      </Button>
    </div>
  );
};

ContactButton.propTypes = {
  isPasswordSite: PropTypes.bool,
  onClick: PropTypes.func.isRequired,
};

export default ContactButton;
