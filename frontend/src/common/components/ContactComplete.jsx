import PropTypes from 'prop-types';
import { Trans, useTranslation } from 'react-i18next';
import receivedImage from '../../assets/img/illust_received.png';
import Button from '../../common/components/Button';

const RECEIVED_IMAGE = receivedImage;

function ContactComplete({ email, setIsCompleted }) {
  const { t } = useTranslation('common');

  return (
    <div className="c-panel">
      <h3 className="c-panel__title c-panel__title--darkGreen">
        {t('相談受付が完了しました')}
      </h3>
      <div className="c-panel__img">
        <img src={RECEIVED_IMAGE} alt="" />
      </div>
      <p className="c-panel__text">
        <Trans
          ns="common"
          i18nKey="専門家より<email>{{email}}</email>に<break>ご連絡いたします。</break>"
          values={{ email }}
          components={{ email: <span className="c-panel__emailAddress" />, break: <span className="c-panel__textBreak" /> }}
        />
      </p>
      <p className="c-panel__annotation c-panel__annotation--center">
        <Trans
          ns="common"
          i18nKey="※メールが届かない場合は、迷惑メールフォルダをご確認いただくか、<btn>もう一度入力</btn>してください。"
          components={{ btn: <Button variant="text" onClick={() => setIsCompleted(false)} /> }}
        />
      </p>
    </div>
  );
}

ContactComplete.propTypes = {
  email: PropTypes.string.isRequired,
  setIsCompleted: PropTypes.func.isRequired,
};

export default ContactComplete;
