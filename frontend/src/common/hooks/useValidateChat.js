import { useState } from 'react';
import { useErrorMessages } from '../messages/error';
import { validateChat } from '../utils';

export const useValidateChat = () => {
  const [error, setError] = useState(null);
  const errorMessages = useErrorMessages();

  const validate = (text) => {
    if (!validateChat(text)) {
      setError(errorMessages.MESSAGE_CONTAINS_EMAIL_ERROR());
      return false;
    }

    if (text.length > 500) {
      setError(errorMessages.MESSAGE_EXCEEDS_MAX_LENGTH());
      return false;
    }
    setError(null);
    return true;
  };

  const clearError = () => {
    setError(null);
  };

  return {
    error,
    validate,
    clearError,
  };
};
