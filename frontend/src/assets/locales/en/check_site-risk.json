{"Webサイトリスク診断日：{{date}}": "Website Risk Assessment Date: {{date}}", "なりすまし診断": "Anti-Impersonation Measures", "診断対象のURL": "URL for Assessment", "他のURLを診断": "<PERSON><PERSON>s Another URL", "早急な対応が必要な項目があります": "Items requiring immediate attention", "概ね安全ですが、引き続き注意を要する状態です": "おおむねあんぜんですが、引き続き注意を要する状態です", "すべての診断項目が安全な状態です": "All assessment items are secure.", "緊急": "Emergency", "高リスク": "High Risk", "中リスク": "Medium Risk", "低リスク": "Low Risk", "情報": "Information", "問題となる異常は見つかりませんでしたが、より詳細にセキュリティ状態をチェックできる詳細診断モードをお勧めします。": "No critical issues were found, but we recommend using the detailed scan mode for a more thorough security check.", "セキュリティに欠陥があり、サイバー攻撃の標的になりやすい状態です。まずは緊急度の高いリスク項目から、セキュリティ対策を実施してください。": "There are security vulnerabilities present, making your system an easy target for cyberattacks. Please implement security measures starting with the high-priority risk items.", "重大かつ緊急性の高いセキュリティの欠陥があり、サイバー攻撃の標的になりやすい状態です。まずは緊急度の高いリスク項目から、セキュリティ対策を実施してください。": "Critical and time-sensitive security vulnerabilities have been detected, making your system a likely target for cyberattacks. Please implement security measures starting with the high-priority risk items.", "安全です": "Safe", "要対策": "Action Required", "要緊急対応": "Urgent Action Required", "要確認": "Verification Needed", "AWS・Google Cloud・Microsoft Azureは使用していません": "You don’t use AWS, Google Cloud, or Azure.", "クラウドの中身も診断<br />(無料お試し)": "<PERSON>an Inside Your Cloud<br />(Free)", "あなたのクラウド、本当に安全ですか？": "Is your cloud safe?", "問題ありません！この状態をキープしましょう。": "Everything looks good! Let’s maintain this state.", "今のところ安全ですが、油断せず対策を考えましょう。": "It's safe for now, but let's stay alert and think about countermeasures.", "このままだと危険です！早めに対策しましょう。": "It’s dangerous if we continue like this! Let’s take action as soon as possible.", "非常に危険です！早急に対策しましょう。": "Extremely dangerous! We must take immediate action.", "要注意": "Caution Required", "危険": "Dangerous", "非常に危険": "Extremely Dangerous", "お申し込みを受け付けいたしました": "Your application has been received", "無料お試しを受け付けいたしました": "Your free trial request has been received", "無料見積を受け付けいたしました": "Your free quote request has been received", "詳細のご案内をメールアドレスに<br />お送りいたします。": "We will send detailed information to your email address.", "メールアドレスにご案内をお送りいたします。": "Instructions will be sent to your email address.", "エラーが発生しました。": "An error has occurred.", "再度お試しください。": "Please try again.", "本診断結果は、{{date}}時点のものです。診断結果は、<btn>利用規約</btn>をご確認のうえ、最終的にはユーザーの皆様のご判断においてご利用ください。": "This diagnostic result is current as of {{date}}. Please review the <btn>terms of service</btn> before using these results, which should ultimately be applied at your own discretion.", "クラウド利用・リスク診断の詳細": "Cloud Usage & Risk Scan — Details", "{{cloud}}のクラウドをご利用中です。クラウド環境のセキュリティ障害の99%<note>※</note>が、お客様の設定ミスなどに起因するようになるといわれています。設定内容をご確認ください。<br /><note>※</note> Gartner社レポート「Is the Cloud Secure?」より": "You’re running on {{cloud}}. Research shows that 99 % of cloud security incidents trace back to customer misconfigurations<note>※</note>. Please double‑check your settings. <note>※</note><PERSON><PERSON>ner, “Is the Cloud Secure?”", "・想定される影響": "Why this matters", "― 実は大事なクラウドリスク": "— cloud risks are real", "クラウドを利用していると、サービスの設定の間違いによって、本来は外部に公開するはずのないデータが見えてしまうことがあります。たとえば、「社内だけで使っているはずのデータが、インターネット上に公開されていた」などが典型的なケースです。クラウドだから安全、というわけではありません。": "Mis‑set services can leak data you never meant to expose. Think “internal‑only files that end up publicly searchable.” A cloud platform isn’t secure by default.", "・対策方法": "What you can do", "どんなデータやサーバーがクラウド上で動いているかを常に把握すること、およびクラウドのセキュリティベストプラクティスを学び、「自分たちのシステムは本当に大丈夫か」を定期的に確認することをおすすめいたします。": "We recommend keeping a continuous inventory of every dataset and server running in your cloud, learning cloud‑security best practices, and regularly asking whether your system is truly secure.", "「まずは、何がどこで動いているのか」を整理し、誤って公開されているものがないかチェックしましょう。万が一の設定ミスが大きなトラブルにつながるのを防ぐためにも、定期的な見直しがとても大切です。セルフチェックのために、自社システムを点検する際に役立つ資料はこちらです。": "First, map out what is running where and check for anything that may have been exposed by mistake. Regular reviews are vital to keep a single misconfiguration from snowballing into a major incident. Use the following resources to guide your self‑check when you audit your own systems.", "- AWS セキュリティのチェックリスト（CIS AWS Foundations Benchmark）": "- AWS Security Checklist (CIS AWS Foundations Benchmark)", "- AWS クラウドセキュリティに関するコラム": "- AWS Cloud Security Articles", "- AWS クラウドベストプラクティスの解説": "- AWS Cloud Best‑Practice Guide", "- Google Cloud セキュリティのチェックリスト": "- Google Cloud Security Checklist", "- Azure セキュリティのチェックリスト": "- Azure Security Checklist", "もし専門家に相談したい場合や、設定ミスを総合的にチェックしたい場合はお気軽に「クラウドの中身も診断」より無料でお試しください。": "Need expert help or a full misconfiguration scan? Try “Scan Inside Your Cloud” free.", "電話番号を入力してください": "Phone Number", "守": "First Name", "安全": "Last Name", "45日間無料お試しSSLサーバ証書": "45-Day Free Test Certificate", "申し込み": "Submit", "お客様の診断結果での概算見積": "Estimated quote based on your diagnostic results", "プロが最新のセキュリティ対策を<br />迅速に代行いたします": "Our professionals will promptly implement the latest security measures on your behalf.", "VMC発行申請": "VMC Issuance Application", "BIMIレコード設定": "BIMI Record Setting", "SPFレコード設定": "SPF Record Setting", "DMARCレコード設定": "DMARC Record Setting", "ブランドTLD利用": "Use of Brand TLD", "類似ドメイン数量": "Number of lookalike domains", "・VMC発行申請（公式ロゴの証明書）とは": "About VMC issuance application (Official Logo Certificate)", "VMC（Verified Mark<br />Certificate）はブランドロゴをメールやウェブに表示するための証明書で、なりすまし対策のためにブランド視認性を高める仕組みです。": "VMC (Verified Mark Certificate) is a certificate for displaying a brand logo in emails and on the web, and is a mechanism for increasing brand visibility to prevent spoofing.", "VMCがないと、BIMIのロゴを使えなかったり、なりすまし防止効果が十分でないこともあります。": "Without a VMC, you cannot use the BIMI logo and your protection from spoofing will be insufficient.", "VMCを取得すると、メールの信頼性がさらにアップします。": "Obtaining a VMC enhances the credibility and trustworthiness of your emails.", "BIMIレコード設定のイメージ": "BIMI Record Setting Example", "・BIMIレコード設定（正規のロゴ表示）とは": "About BIMI Record Setting (official logo display)", "BIMI（Brand Indicators for Message<br />Identification）は、メール送信者の公式ブランドロゴを表示する仕組みです。設定がないと、公式のメールかどうか判断しにくくなり、偽のメールが信用されやすくなります。": "BIMI (Brand Indicators for Message Identification) is a system that enables the display of an email sender’s official brand logo. Without BIMI, recipients may find it difficult to distinguish legitimate emails from fake ones, increasing the likelihood of spoofed messages being trusted.", "BIMIを設定すれば、なりすましメールを見破りやすくなります。": "BIMI helps recipients visually verify legitimate emails, making spoofed messages easier to detect.", "SPFレコード設定のイメージ": "SPF Record Setting Example", "・SPFレコード設定とは": "About SPF Record Setting", "SPFレコードは、「このドメインから送信してよいメールサーバー」を指定する設定です。これにより、受信側が正規の送信元かどうかを確認できます。SPFを設定していないと、第三者が勝手に自社のドメインを使ってメールを送ることができ、なりすましメールのリスクが高まります。設定することで、不正な送信元からのメールをブロックし、信頼性の高いメール環境を維持できます。": "An SPF record specifies which mail servers are authorized to send emails on behalf of your domain. This enables receiving servers to verify whether the sending server is legitimate. Without SPF, third parties could send emails using your domain without authorization, increasing the risk of spoofed messages. By implementing SPF, you can block such unauthorized senders and maintain a secure and trustworthy email environment.", "DMARCレコード設定のイメージ": "DMARC Record Setting Example", "・DMARCレコード設定とは": "About DMARC Record Setting", "DMARCレコードは、SPFやDKIMの認証結果を基に、「認証に失敗したメールをどう処理するか」を決めるルールです。これがないと、なりすましメールがそのまま届く可能性があります。DMARCを設定すれば、認証に失敗したメールを拒否したり、迷惑メールフォルダに振り分けたりでき、なりすまし対策が強化されます。また、レポート機能により、不正な送信の状況を把握し、より適切な対策を取ることが可能になります。": "A DMARC record defines policies for handling emails that fail authentication, based on SPF and DKIM results. Without DMARC, spoofed emails may be delivered as is. By setting up DMARC, you can reject emails that fail authentication or sort them into the spam folder, strengthening your defenses against spoofing. In addition, the reporting function allows you to understand the status of fraudulent sending and take more appropriate measures.", "・ブランドTLDとは": "About Brand TLD", "ブランドTLDとは、「.com」や「.jp」ではなく、企業専用のドメイン（例：.gmo）」を使うことです。": "A Brand TLD is a custom top-level domain dedicated to a specific brand (e.g., .gmo), instead of common ones like .com or .jp.", "偽物サイトを作られにくくなり、なりすましのリスクを減らすことができます。": "This makes it harder for malicious parties to create counterfeit websites, thereby reducing the risk of impersonation.", "ブランドTLDについて詳しく知る": "Details about Brand TLD", "・見つかった類似ドメイン（10件のみ表示しています）": "Detected Lookalike Domains (Top 10 Results Shown)", "・類似ドメイン数量とは": "About the Number of Lookalike Domains", "あなたの会社のドメイン（例：example.com）と似たドメインがどれくらいあるかを示します。": "This shows how many domains are similar to your company's domain (e.g. example.com).", "例えば、examp1e.com（数字の1が入る） や example.co（.coドメイン）などがあると、詐欺サイトを作られやすくなります。類似ドメインが多いほど、注意が必要です。": "For example, lookalike domains such as exam1e.com (with the number 1) and example.co (.co domain) are used by fake sites. A higher number of lookalike domains increases the risk of impersonation, so extra vigilance is recommended.", "対象ドメインのIPアドレスが確認できないため、診断を実施できませんでした。": "Unable to perform assessment as the target domain's IP address could not be verified.", "DNS設定を確認のうえ、再度診断を実施してください。": "Please check DNS settings and try again.", "閉じる": "Close", "もっと見る": "Show more", "本脆弱性診断では、診断対象サイトに影響を与えない範囲で<about>「ネットワーク診断・Webアプリケーション診断・CMS診断」</about>を実施しています。診断結果が「安全です」の場合、「一定の不正アクセス対策」が講じられ、一定の安全性が確保されていることを示します。「より正確な結果」「検知項目に対する対策」をお求めの場合は、以下「対策する(無料見積)」より<contact>お問い合わせください。</contact>": "This scan includes <about>network, web application, and CMS diagnostics</about>, performed in a way that does not affect your website. If the result is “Secure,” it indicates that basic cybersecurity measures have been implemented and a certain level of security is in place. For more accurate results or solutions for detected issues, please contact us via <contact>“Take Action (Free Quote)”</contact> below.", "ご利用中のSSLは有効期限が十分に残っております。": "Your current SSL certificate has sufficient validity remaining.", "安心です": "It's safe", "使用中の証明書がドメインと一致していません。早急に設定を見直してください。": "The certificate in use does not match the domain. Please review your configuration immediately.", "不一致": "Discrepancy", "ドメインと異なる証明書": "SSL certificate is issued for a different domain.", "証明書が無効になっています。早急に再設定または更新を行ってください。": "The certificate is invalid. Please reconfigure or renew it as soon as possible.", "無効": "Invalid", "無効な証明書": "Invalid Certificate", "信頼性の低い無料SSLをご利用中です。変更を推奨します。": "You are using a low-trust free SSL certificate. A change is recommended", "変更推奨": "Change Recommended", "早急に有効期限をご確認ください。": "Please check the validity period immediately.", "SSL証明書が見つかりませんでした": "SSL certificate not found.", "無料SSLの企業利用はご注意ください": "Caution: Use of free SSL certificates by businesses is not recommended.", "フィッシングサイトの95%以上は無料SSLを利用しています": "Over 95% of phishing sites use free SSL certificates.", "無料SSLは手軽に取得できるため、多くのフィッシングサイトが悪用しています。": "Free SSL certificates are easily obtainable, making them a frequent target for misuse by phishing sites.", "企業が利用すると、信頼性の低さ・更新漏れ・サポートなしといったリスクがあります。": "Using them for business purposes carries risks such as low trust, missed renewals, and lack of support.", "企業サイトには、有償のSSLを推奨します。「無料」で選ぶのではなく、信頼性で選ぶことが重要です。": "Paid SSL certificates are recommended for corporate websites. Choose based on trustworthiness, not on being “free.”", "（無料SSL）を利用中": "Using a Free SSL Certificate", "今すぐwebを安全に<br />テストSSL（無料）": "Secure your website　Test Certificate", "設定が保存されました": "Setting<PERSON> saved", "定期診断を無償で行い、メールでお知らせします。": "Free regular assessments with email notifications.", "定期診断": "Regular Assessment", "{{count}}ヶ月毎_one": "every month", "{{count}}ヶ月毎_other": "every {{count}} months", "次回診断日：{{date}}": "Next Assessment Date: {{date}}", "以下2点より、Webサイトを常に安全に保つためには月1回の診断を推奨します。<br />①2024年には「1日あたり100個以上※1」の新規脆弱性が確認されていること<br />②システム改修/更新により新たな脆弱性が発生する可能性があること<br />※1 出典：https://www.cvedetails.com/browse-by-date.php": "We recommend monthly scans to keep your website secure based on the following two points:<br />1. Over 100 new vulnerabilities are identified daily in 2024. (*1)<br />2. System updates or modifications may introduce new vulnerabilities.\n*1: https://www.cvedetails.com/browse-by-date.php", "本診断結果は、{{date}}時点のものです。診断結果は、<ag>利用規約</ag>を参照してください。": "This diagnostic result is current as of {{date}}. Please refer to the <ag>terms of service</ag> for details.", "有効期限：<sp>{{date}}まで（{{remainder}}）</sp>": "Valid until: <sp>{{date}}</sp>", "有効期限：<alert>{{date}}まで（期限切れ）</alert>": "Valid until: <alert>{{date}} Expired</alert>", "姓": "Last Name", "名": "First Name", "電話番号": "Phone Number", "※": "*", "診断実施中": "Assessment in Progress", "IPアドレスが確認できません": "IP Address Not Found", "クラウド利用・リスク診断": "Cloud Usage & Risk Scan", "また、<btn>Googleの調査「H1 2025 Threat Horizons Report」</btn>でも、こうした設定ミスを狙う攻撃が、2024年末にかけて増えていく見込みであると報告されています。今後ますます注意が必要です。": "Google’s <btn>“H1 2025 Threat Horizons Report”</btn> also warns that attacks targeting these mistakes will keep rising through late 2024 and beyond, so extra care is critical.", "VMC発行申請のイメージ": "VMC Issuance Application Example", "ブランドTLDのイメージ": "Brand TLD Example", "例えば、「.gmo」ドメインを持っていれば、他の人は取得できません。": "For example, if you own the “.gmo” brand TLD, no one else can register any domain under “.gmo”.", "フィッシング審査なし 保証・サポートなし": "No phishing review / No warranty or support", "¥{{price}}<unit>程度</unit>": "<unit>Around</unit> ¥{{price}}", "Webサイト脆弱性診断": "Website Vulnerability Scan", "詳細に診断する": "Perform a Detailed Scan", "対策する(無料見積)": "Take Action (Free Quote)", "診断を実施中です。結果が準備でき次第、メールでお知らせいたします。": "Assessment in progress. Results will be sent via email when ready.", "※診断には数日かかる場合があります": "*Assessment may take several days", "今すぐSSLを更新してください。": "Please renew your SSL certificate now", "※各項目の詳細を確認できる期限が過ぎています。<sp>ご確認いただくには<btn>再度認診</btn>を行ってください。</sp>": "※The period to check details for each item has expired. <sp>To review, please <btn>conduct another assessment</btn></sp>.", "再度診断する": "Assessment again", "あり": "Something", "なし": "None", "実在証明・盗聴防止（SSL）診断": "SSL verification/anti-eavesdropping diagnosis", "利用なし": "Not in use"}