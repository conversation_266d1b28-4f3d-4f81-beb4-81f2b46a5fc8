@charset "utf-8";

@use "../global/variables" as *;
@use "../global/mixin" as *;

.p-chat {
  padding: 20px 0 56px;
  background: $color-white;
}
.p-chatPanel {
  position: relative;
  padding: 0 40px;
  border-radius: $border-radius-card;
  height: calc(100vh - 186px);
  &__head {
    border-bottom: 1px solid $color-darkGreen;
    padding-bottom: 20px;
    margin-bottom: 20px;
  }
  &__body {
    height: 100%;
  }
  &__title {
    font-weight: 600;
    display: grid;
    grid-template-columns: auto 1fr;
    align-items: center;
    column-gap: 12px;
    font-size: 24px;
  }
  &__text {
    font-size: 14px;
    margin-left: 56px;
    color: $color-darkGreen;
  }
  &__action {
    position: absolute;
    top: 0;
    right: 0;
    display: inline-flex;
    column-gap: 20px;
  }
  &__loading {
    margin-left: 48px;
  }
  &__footer {
    margin-top: 6px;
  }
  &__note {
    font-size: 12px;
    color: $color-darkGreen;
    text-align: center;
  }
  &__error {
    display: inline-flex;
    align-items: flex-start;
    column-gap: 4px;
    font-size: 12px;
    color: $color-error;
    margin-left: 24px;
    & span {
      margin-top: 3px;
    }
  }
}
/* 小窓のchat css */
.p-chatPanelSmall {
  &__head {
    position: absolute;
    top: 0;
    left: 40px;
    border-bottom: 1px solid $color-darkGreen;
    z-index: $z-index-2;
    width: calc(100% - 20px);
    padding-bottom: 10px;
    margin-bottom: 10px;
    top: 10px;
    left: 10px;
  }
  &__body {
    height: 100%;
  }
  &__title {
    font-weight: 600;
    display: grid;
    grid-template-columns: auto 1fr;
    align-items: center;
    column-gap: 12px;
    font-size: 16px;
  }
  &__text {
    font-size: 10px;
    color: $color-gray80;
    margin-left: 50px;
  }
  &__action {
    position: absolute;
    top: 0;
    right: 0;
    display: inline-flex;
    column-gap: 16px;
  }
  &__loading {
    margin-left: 48px;
  }
  &__footer {
    margin-top: 4px;
  }
  &__note {
    font-size: 12px;
    color: $color-darkGreen;
    text-align: center;
  }
  &__error {
    display: inline-flex;
    align-items: flex-start;
    column-gap: 4px;
    font-size: 12px;
    color: $color-error;
    margin-left: 24px;
    & span {
      margin-top: 3px;
    }
  }
}
.p-chatMs {
  display: flex;
  column-gap: 20px;
  align-items: flex-start;
  &--right {
    justify-content: flex-end;
    margin-left: auto;
  }
  &__message {
    width: calc(100% - 56px);
  }
  &__feedback {
    display: inline-flex;
    column-gap: 10px;
    margin-top: 10px;
  }
}
.p-chatMs + .p-chatMs {
  margin-top: 50px;
}
.p-chatFeedback {
  display: inline-flex;
  column-gap: 10px;
  margin-top: 16px;
  position: absolute;
  bottom: 64px;
  left: 0;
  z-index: $z-index-2;
}
/* ui kit 上書きcss class名はui kitまま */
.cs-main-container .cs-chat-container {
  background: transparent;
  flex-basis: 100%;
}
.cs-main-container {
  border: solid 1px transparent;
}
.cs-message--outgoing .cs-message__content {
  margin-left: auto;
}
.cs-message {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  border-radius: $border-radius-card;
  padding: 0;
  color: $color-darkGreen;
  &--incoming {
    align-self: flex-start;
  }
  &--outgoing {
    align-self: flex-end;
  }
  &__content {
    font-family: inherit;
    font-size: inherit;
    padding: 8px 20px 10px;
  }
  &__content-wrapper {
    width: 100%;
  }
}
.cs-main-container,
.cs-message .cs-main-container {
  border: solid 1px transparent;
  background: transparent;
}
.p-chatPanelSmall__body .cs-message-list {
  margin-top: 100px;
}
.cs-message-list {
  margin-bottom: 20px;
  background-color: transparent;
}
.cs-message .cs-message__custom-content {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}
.cs-message--outgoing .cs-message__content {
  color: $color-black;
  background-color: $color-darkGreen10;
  border-radius: $border-radius-card;
}
.cs-message--incoming .cs-message__content {
  color: $color-black;
  background-color: transparent;
  border-radius: $border-radius-card;
  padding: 0;
}
.cs-message:not(:only-child) {
  margin: 0;
}
/* input */
.cs-chat-container .cs-message-input {
  border-color: transparent;
  background-color: $color-gray10;
  border-radius: $border-radius-round;
}
.cs-message-input {
  background-color: $color-gray10;
  border-radius: $border-radius-round;
  &__content-editor {
    background-color: $color-gray10;
    &-wrapper {
      border-radius: $border-radius-round;
      background-color: $color-gray10;
      padding: 0.6em 0.8em 0.6em 1.6em;
    }
  }
}
.cs-message-input__content-editor {
  font-family: inherit;
  color: $color-black;
}
.cs-message-input__tools {
  flex-direction: column;
  justify-content: center;
  margin-right: 4px;
}
.cs-button--attachment {
  display: none;
}
.cs-message-input__tools .cs-button {
  font-size: inherit;
}
.cs-message-input__tools:last-child .cs-button:last-child {
  margin: auto;
}
.cs-button {
  padding: 0;
  &--send {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    color: $color-white;
    background-color: $color-darkGreen;
    transition: all 0.3s;
    margin-right: 4px !important;
    @media (min-width: $window_tb_min) {
      &:hover {
        opacity: 0.8;
      }
    }
    &::before {
      content: "\e90c";
      font-family: "security-icon";
      font-size: 32px;
      margin-left: 6px;
    }
    & svg {
      display: none;
    }
  }
}

/* scroll bar */
.cs-message-input__content-editor-container {
  background-color: $color-darkGreen10;
}
.ps__thumb-y {
  background-color: $color-darkGreen-alpha20;
}
.ps__rail-y:hover > .ps__thumb-y,
.ps__rail-y:focus > .ps__thumb-y,
.ps__rail-y.ps--clicking .ps__thumb-y {
  background-color: $color-darkGreen-alpha20;
}
.ps .ps__rail-x:hover,
.ps .ps__rail-y:hover,
.ps .ps__rail-x:focus,
.ps .ps__rail-y:focus,
.ps .ps__rail-x.ps--clicking,
.ps .ps__rail-y.ps--clicking {
  background-color: $color-darkGreen10;
  opacity: 0.9;
}

/* sp style */
@include for-device("sp") {
  .p-chat {
    padding: 0;
    &::before {
      opacity: 0;
    }
  }
  .p-chatPanel {
    margin-left: calc(50% - 50vw);
    margin-right: calc(50% - 50vw);
    padding: 10px 10px 50px 10px;
    border-radius: 0;
    height: calc(100dvh - 110px);
    &__head {
      width: 100vw;
      margin: 0 calc(50% - 50vw) 20px;
      padding-left: 30px;
      padding-right: 10px;
    }
    &__boardHead {
      width: 100vw;
      margin: 0 calc(50% - 50vw) 20px;
      padding-left: 30px;
      padding-right: 10px;
    }
    &__title {
      font-size: 16px;
      font-weight: 300;
    }
    &__footer {
      margin-top: 4px;
    }
  }
  .p-chatPanelSmall {
    &__title {
      font-size: 15px;
      column-gap: 8px;
    }
    &__action {
      column-gap: 12px;
    }
    &__text {
      margin-left: 46px;
    }
  }
  /* ui kit 上書きcss */
  .cs-message-list__scroll-wrapper {
    padding: 0 1.2em 0 0;
  }

  .p-chatMs + .p-chatMs {
    margin-top: 40px;
  }
  .cs-message-input__content-editor-container {
    font-size: 16px;
  }
}
