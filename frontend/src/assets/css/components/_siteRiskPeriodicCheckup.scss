@charset "utf-8";

@use "../global/variables" as *;
@use "../global/mixin" as *;
@use "../global/animation" as *;

.c-siteRiskPeriodicCheckup {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px 20px;
  background-color: $color-white;
  border-top: 1px solid $color-gray20;
  padding-top: 20px;
  &__textWrap {
    display: flex;
    column-gap: 8px;
  }
  &__icon {
    margin-top: 2px;
  }
  &__text {
    font-weight: 600;
    font-size: 16px;
    color: $color-darkGreen;
  }
  &__list {
    display: grid;
    row-gap: 8px;
    margin-left: auto;
  }
  &__date {
    font-size: 12px;
    color: $color-darkGreen;
    text-align: right;
  }
}
.c-siteRiskPeriodicCheckupElement {
  padding: 4px 8px;
  border: 1px solid $color-gray20;
  border-radius: $border-radius-card-half;
  display: flex;
  align-items: center;
  column-gap: 10px;
  &__label {
    font-size: 14px;
  }
  &__toggle {
    display: flex;
  }
  &__select {
    flex-shrink: 0;
  }
}

/* sp style */
@include for-device("sp") {
  .c-siteRiskPeriodicCheckup {
    display: grid;
    row-gap: 10px;
    &__list {
      margin-left: 0;
    }
  }
  .c-siteRiskPeriodicCheckupElement {
    &__label {
      margin-right: auto;
    }
  }
}
