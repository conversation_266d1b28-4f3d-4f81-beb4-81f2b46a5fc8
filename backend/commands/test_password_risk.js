import crypto from 'crypto';
import { COLLECTION_HIBP, COLLECTION_REGULARLY_PASSWORD_CONFIGURATION } from '../constants/collections.js';
import { THIRTY_DAYS_MILLISECOND, TTL_MILLISECOND } from '../constants/constants.js';
import { saveDoc } from '../providers/firestore.js';
import { encrypt, hashEmail } from '../services/cryptography.js';

const createUniqueEmail = (keyName) => {
  const uuid = crypto.randomUUID();
  return `example-${keyName}-${uuid}@gmo.jp`;
};

const BREACH_TEMPLATES = {
  single_verified: {
    Name: 'Peatix',
    Title: 'Peatix',
    Domain: 'peatix.com',
    BreachDate: '2019-01-20',
    AddedDate: '2020-12-06T22:53:53Z',
    ModifiedDate: '2020-12-06T22:53:53Z',
    PwnCount: 4227907,
    Description: 'In January 2019, the event organising platform Peatix suffered a data breach. The incident exposed 4.2M email addresses, names and salted password hashes.',
    LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/Peatix.png',
    DataClasses: ['Email addresses', 'Names', 'Passwords'],
  },

  large_collection: {
    Name: 'Collection1',
    Title: 'Collection #1',
    Domain: '',
    BreachDate: '2019-01-07',
    AddedDate: '2019-01-16T21:46:07Z',
    ModifiedDate: '2019-01-16T21:50:21Z',
    PwnCount: 772904991,
    Description: 'In January 2019, a large collection of credential stuffing lists was discovered being distributed on a popular hacking forum.',
    LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/List.png',
    DataClasses: ['Email addresses', 'Passwords'],
  },

  sensitive_adult: {
    Name: 'AdultFriendFinder',
    Title: 'Adult FriendFinder',
    Domain: 'adultfriendfinder.com',
    BreachDate: '2016-10-01',
    AddedDate: '2016-11-14T00:00:00Z',
    ModifiedDate: '2016-11-14T00:00:00Z',
    PwnCount: *********,
    Description: 'In October 2016, the adult website Adult FriendFinder was hacked and over 400 million accounts were exposed.',
    LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/AdultFriendFinder.png',
    DataClasses: ['Dates of birth', 'Email addresses', 'Ethnicities', 'Genders', 'Names', 'Passwords', 'Phone numbers', 'Sexual orientations', 'Usernames'],
  },

  email_only: {
    Name: 'EmailOnlyBreach',
    Title: 'Email Only Data Breach',
    Domain: 'emailonly.com',
    BreachDate: '2024-01-10',
    AddedDate: '2024-02-01T09:15:00Z',
    ModifiedDate: '2024-02-01T09:15:00Z',
    PwnCount: 5000000,
    Description: 'In January 2024, a data breach exposed email addresses and names but did not contain any password information.',
    LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/List.png',
    DataClasses: ['Email addresses', 'Names'],
  },

  multiple: [
    {
      Name: 'Adobe',
      Title: 'Adobe',
      Domain: 'adobe.com',
      BreachDate: '2013-10-04',
      AddedDate: '2013-12-04T00:00:00Z',
      ModifiedDate: '2013-12-04T00:00:00Z',
      PwnCount: *********,
      Description: 'In October 2013, 153 million Adobe accounts were breached with each containing an internal ID, username, email, encrypted password and a password hint in plain text.',
      LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/Adobe.png',
      DataClasses: ['Email addresses', 'Password hints', 'Passwords', 'Usernames'],
    },
    {
      Name: 'Dropbox',
      Title: 'Dropbox',
      Domain: 'dropbox.com',
      BreachDate: '2012-07-01',
      AddedDate: '2016-08-31T00:19:19Z',
      ModifiedDate: '2016-08-31T00:19:19Z',
      PwnCount: ********,
      Description: 'In mid-2012, Dropbox suffered a data breach which exposed the stored credentials of tens of millions of their customers.',
      LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/Dropbox.png',
      DataClasses: ['Email addresses', 'Passwords'],
    },
    {
      Name: 'Collection1',
      Title: 'Collection #1',
      Domain: '',
      BreachDate: '2019-01-07',
      AddedDate: '2019-01-16T21:46:07Z',
      ModifiedDate: '2019-01-16T21:50:21Z',
      PwnCount: 772904991,
      Description: 'In January 2019, a large collection of credential stuffing lists was discovered being distributed on a popular hacking forum.',
      LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/List.png',
      DataClasses: ['Email addresses', 'Passwords'],
    }, {
      Name: 'AdultFriendFinder',
      Title: 'Adult FriendFinder',
      Domain: 'adultfriendfinder.com',
      BreachDate: '2016-10-01',
      AddedDate: '2016-11-14T00:00:00Z',
      ModifiedDate: '2016-11-14T00:00:00Z',
      PwnCount: *********,
      Description: 'In October 2016, the adult website Adult FriendFinder was hacked and over 400 million accounts were exposed.',
      LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/AdultFriendFinder.png',
      DataClasses: ['Dates of birth', 'Email addresses', 'Ethnicities', 'Genders', 'Names', 'Passwords', 'Phone numbers', 'Sexual orientations', 'Usernames'],
    }, {
      Name: 'EmailOnlyBreach',
      Title: 'Email Only Data Breach',
      Domain: 'emailonly.com',
      BreachDate: '2024-01-10',
      AddedDate: '2024-02-01T09:15:00Z',
      ModifiedDate: '2024-02-01T09:15:00Z',
      PwnCount: 5000000,
      Description: 'In January 2024, a data breach exposed email addresses and names but did not contain any password information.',
      LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/List.png',
      DataClasses: ['Email addresses', 'Names'],
    },
  ],
};

const BASE_FLAGS = {
  IsVerified: false,
  IsFabricated: false,
  IsSensitive: false,
  IsRetired: false,
  IsSpamList: false,
  IsMalware: false,
  IsStealerLog: false,
};

const createFlags = (overrides = {}) => ({ ...BASE_FLAGS, ...overrides });

const createPattern = (key, description, template, flagOverrides = {}, expired = false) => ({
  key,
  description,
  template,
  flags: template ? createFlags(flagOverrides) : undefined,
  expired,
  ...(template === null && { result: [] }),
});

const BASE_PATTERNS = [
  // 基本パターン
  createPattern('NO_LEAK', 'No breach - safe email address', null),

  // 単一の漏洩バリエーション
  createPattern('LEAK_VERIFIED', 'Single verified breach with passwords', 'single_verified', { IsVerified: true }),
  createPattern('LEAK_UNVERIFIED', 'Single unverified breach', 'single_verified'),
  createPattern('LEAK_FABRICATED', 'Single fabricated breach', 'single_verified', { IsFabricated: true }),
  createPattern('LEAK_RETIRED', 'Single retired breach', 'single_verified', { IsVerified: true, IsRetired: true }),

  // 高度な重要度パターン
  createPattern('LEAK_SPAM_LIST', 'High severity spam list', 'large_collection', { IsSpamList: true }),
  createPattern('LEAK_MALWARE', 'Malware-related breach', 'single_verified', { IsVerified: true, IsMalware: true }),
  createPattern('LEAK_STEALER_LOG', 'Info stealer log collection', 'large_collection', { IsVerified: true, IsStealerLog: true }),

  // 機密パターン
  createPattern('LEAK_SENSITIVE', 'Sensitive adult site breach', 'sensitive_adult', { IsVerified: true, IsSensitive: true }),
  createPattern('LEAK_SENSITIVE_UNVERIFIED', 'Sensitive unverified breach', 'sensitive_adult', { IsSensitive: true }),

  // パスワードなしパターン
  createPattern('LEAK_NO_PASSWORD', 'Breach without passwords', 'email_only', { IsVerified: true }),

  // 複数漏洩パターン
  createPattern('LEAK_MULTIPLE_VERIFIED', 'Multiple verified breaches', 'multiple', { IsVerified: true }),
  {
    key: 'LEAK_MULTIPLE_MIXED',
    description: 'Multiple breaches with mixed flags',
    template: 'multiple_small',
    flags: [
      createFlags({ IsVerified: true }),
      createFlags({ IsSpamList: true }),
    ],
    expired: false,
  },
];

const EXPIRABLE_PATTERN_KEYS = ['LEAK_VERIFIED', 'LEAK_SPAM_LIST', 'LEAK_SENSITIVE'];

const createExpiredPattern = basePattern => ({
  ...basePattern,
  key: `${basePattern.key}_EXPIRED`,
  description: `Expired ${basePattern.description.toLowerCase()}`,
  expired: true,
});

const PATTERN_DEFINITIONS = [
  ...BASE_PATTERNS,
  ...BASE_PATTERNS
    .filter(pattern => EXPIRABLE_PATTERN_KEYS.includes(pattern.key))
    .map(createExpiredPattern),
];

const configurations = {
  noRegularNoNoti: {
    isRegularly: false,
    interval: 1,
    isNotification: false,
    nextCheckedAt: null,
  },
  pastDate: {
    isRegularly: true,
    interval: 3,
    isNotification: true,
    nextCheckedAt: new Date(Date.parse('2025-03-10') - THIRTY_DAYS_MILLISECOND).toISOString(),
  },
  todayDate: {
    isRegularly: true,
    interval: 3,
    isNotification: false,
    nextCheckedAt: new Date().toISOString(),
  },
  futureDate: {
    isRegularly: false,
    interval: 6,
    isNotification: false,
    nextCheckedAt: new Date(new Date().getTime() + THIRTY_DAYS_MILLISECOND).toISOString(),
  },
  thresholdDate: {
    isRegularly: true,
    interval: 1,
    isNotification: true,
    nextCheckedAt: new Date(Date.parse('2025-03-10')).toISOString(),
  },
  regularInterval1: {
    isRegularly: true,
    interval: 1,
    isNotification: true,
    nextCheckedAt: new Date(new Date().getTime() + THIRTY_DAYS_MILLISECOND).toISOString(),
  },
  regularInterval6: {
    isRegularly: true,
    interval: 6,
    isNotification: false,
    nextCheckedAt: new Date(new Date().getTime() + (6 * THIRTY_DAYS_MILLISECOND)).toISOString(),
  },
  noRegularWithInterval: {
    isRegularly: false,
    interval: 3,
    isNotification: true,
    nextCheckedAt: null,
  },
};

const generateBreachData = (template, flags) => {
  if (!template) return [];

  const baseTemplate = BREACH_TEMPLATES[template];
  if (!baseTemplate) return [];

  // 複数の漏洩を処理
  if (Array.isArray(baseTemplate)) {
    return baseTemplate.map((breach, index) => {
      const flagsToUse = Array.isArray(flags) ? (flags[index] || flags[0]) : flags;
      return {
        ...breach,
        ...flagsToUse,
        IsSubscriptionFree: false,
      };
    });
  }

  // 単一の漏洩を処理
  return [{
    ...baseTemplate,
    ...flags,
    IsSubscriptionFree: false,
  }];
};

// パターンからDUMMYオブジェクトを生成する関数
const generateDummyData = () => {
  const DUMMY = {};

  PATTERN_DEFINITIONS.forEach((pattern) => {
    const now = new Date();
    const isExpired = pattern.expired;

    DUMMY[pattern.key] = {
      createdAt: now.toISOString(),
      expiredAt: isExpired
        ? new Date('2024-12-31T23:59:59.000Z').toISOString()
        : new Date('2999-12-31T23:59:59.000Z').toISOString(),
      result: pattern.template ? generateBreachData(pattern.template, pattern.flags) : [],
    };
  });

  return DUMMY;
};

const run = async () => {
  const DUMMY = generateDummyData();

  for (const configKey in configurations) {
    const configuration = configurations[configKey];

    for (const keyName in DUMMY) {
      const dataHibp = { ...DUMMY[keyName] };
      const uniqueEmail = createUniqueEmail(keyName);
      dataHibp.email = uniqueEmail;

      const now = new Date();
      const isExpired = keyName.includes('EXPIRED');
      const expiredAt = isExpired ? new Date('2024-12-31T23:59:59.000Z') : new Date(now.getTime() + TTL_MILLISECOND);
      const hashedEmail = await hashEmail(uniqueEmail);

      const code = await encrypt(
        JSON.stringify({ email: uniqueEmail, expiredAt: expiredAt.getTime() }),
        process.env.SECRET_CRYPTOGRAPHY_PASSWORD,
        process.env.SECRET_CRYPTOGRAPHY_SALT,
      );

      const names = dataHibp.result
        .filter(r => r.DataClasses.includes('Passwords'))
        .map(r => r.Name);

      const count = names.length;

      await saveDoc({
        collection: COLLECTION_HIBP,
        docId: code,
        data: {
          ...dataHibp,
          configuration,
        },
      });

      await saveDoc({
        collection: COLLECTION_REGULARLY_PASSWORD_CONFIGURATION,
        docId: hashedEmail,
        data: {
          encryptedEmail: uniqueEmail,
          isRegularly: configuration.isRegularly,
          interval: configuration.interval,
          nextCheckedAt: configuration.nextCheckedAt,
          isNotification: configuration.isNotification,
          history: [{
            createdAt: dataHibp.createdAt,
            code: code,
            names: names,
            count: count,
          }],
        },
      });

      console.log({
        email: uniqueEmail,
        createdAt: dataHibp.createdAt,
        expiredAt: dataHibp.expiredAt,
        type: keyName,
        configType: configKey,
        configuration,
        expired: isExpired,
        leakedSites: names.join(', ') || 'None',
        leakCount: count,
        url: `${process.env.HOST}${process.env.PATH_PREFIX}/check/password/?code=${code}`,
      });
    }
  }
};

run().catch(console.error);
