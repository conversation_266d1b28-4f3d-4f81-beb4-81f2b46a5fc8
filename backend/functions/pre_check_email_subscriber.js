import { publishCheckEmail } from '../handlers/post_email.js';
import { priDecrypt } from '../services/cryptography.js';

const subscriber = async ({ data }) => {
  const { encryptedEmail, isRegularly = false, name, lang = 'ja' } = JSON.parse(Buffer.from(data.message.data, 'base64').toString());
  if (!encryptedEmail) {
    console.error(new Error('encryptedEmail is required'));
    return;
  }

  const email = await priDecrypt(encryptedEmail, process.env.SECRET_PRIVATE_KEY);
  await publishCheckEmail(email, isRegularly, name, lang);
};

export default subscriber;
