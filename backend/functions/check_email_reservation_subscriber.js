import { COLLECTION_REGULARLY_PASSWORD_CONFIGURATION, COLLECTION_USER_CONFIGURATION } from '../constants/collections.js';
import { getDoc, saveDoc } from '../providers/firestore.js';
import { hashEmail, pubEncrypt } from '../services/cryptography.js';
import { createPasswordConfigurationSink } from '../services/sink.js';

const subscriber = async ({ data }) => {
  const { email, createdAt, code, isRegularly = false, name, lang = 'ja' } = JSON.parse(Buffer.from(data.message.data, 'base64').toString());

  if (isRegularly || !!name) return;

  if (!email || !createdAt || !code) {
    console.error(new Error('email, createdAt and code are required'));
    return;
  }

  const encryptedEmail = await pubEncrypt(email, process.env.SECRET_PUBLIC_KEY);
  const hashedEmail = await hashEmail(email);

  const configId = hashedEmail;
  const configDoc = await getDoc({ collection: COLLECTION_USER_CONFIGURATION, docId: configId });
  if (!configDoc.exists) {
    const config = { encryptedEmail, isSiteRiskNotification: false, isPasswordNotification: false };
    await saveDoc({ collection: COLLECTION_USER_CONFIGURATION, docId: configId, data: config });
  }

  const passwordId = hashedEmail;
  const passwordDoc = await getDoc({ collection: COLLECTION_REGULARLY_PASSWORD_CONFIGURATION, docId: passwordId });

  const nextCheckedAt = new Date(createdAt);

  if (passwordDoc.exists) {
    const { interval } = passwordDoc.data();

    // password_history_subscriber との順番が保証できないため
    if (interval === undefined) {
      nextCheckedAt.setMonth(nextCheckedAt.getMonth() + 1);

      const password = { encryptedEmail, isRegularly: true, interval: 1, nextCheckedAt: nextCheckedAt.toISOString(), lang };
      await saveDoc({ collection: COLLECTION_REGULARLY_PASSWORD_CONFIGURATION, docId: passwordId, data: password });

      await createPasswordConfigurationSink(email, true, 1, nextCheckedAt);
    }
  } else {
    nextCheckedAt.setMonth(nextCheckedAt.getMonth() + 1);

    const password = { encryptedEmail, isRegularly: true, interval: 1, nextCheckedAt: nextCheckedAt.toISOString(), lang };
    await saveDoc({ collection: COLLECTION_REGULARLY_PASSWORD_CONFIGURATION, docId: passwordId, data: password });

    await createPasswordConfigurationSink(email, true, 1, nextCheckedAt);
  }
};

export default subscriber;
