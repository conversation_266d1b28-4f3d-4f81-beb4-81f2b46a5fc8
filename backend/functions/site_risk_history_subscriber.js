import { FieldValue } from '@google-cloud/firestore';
import { COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION } from '../constants/collections.js';
import { getDoc, runTransaction, saveDoc } from '../providers/firestore.js';
import { hash } from '../services/cryptography.js';
import { toCamelCaseObject } from '../services/string.js';

const subscriber = async ({ data }) => {
  const { email, fqdn, result, lang = 'ja' } = JSON.parse(Buffer.from(data.message.data, 'base64').toString());
  if (!email || !result) {
    console.error(new Error('email and result are required'));
    return;
  }

  const siteRiskId = await hash(`${email}:${fqdn}`);
  let siteRiskDoc = await getDoc({ collection: COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION, docId: siteRiskId });

  let code;
  const updating = Object.keys(result).reduce((pre, cur) => {
    const { sink, code: _code, expired_at, hashed_email, ...rest } = result[cur];
    code = _code;
    return { ...pre, [cur]: { ...toCamelCaseObject(rest) } };
  }, {});

  if (!siteRiskDoc.exists) {
    await saveDoc({ collection: COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION, docId: siteRiskId, data: { history: FieldValue.arrayUnion({ code }), lang } });
  }

  siteRiskDoc = await getDoc({ collection: COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION, docId: siteRiskId });

  await runTransaction(async (t) => {
    const doc = await t.get(siteRiskDoc.ref);
    const { history = [] } = doc.data();

    let newHistory;
    if (history.find(h => h.code === code)) {
      newHistory = history.map((h) => {
        if (h.code === code) return { ...h, ...updating };
        return h;
      });
    } else {
      newHistory = [...history, { code, ...updating }];
    }

    t.update(siteRiskDoc.ref, { history: newHistory, lang });
  });
};

export default subscriber;
