import { PubSub } from '@google-cloud/pubsub';
import { COLLECTION_NDS } from '../constants/collections.js';
import { CVE_NAMES } from '../constants/cves.js';
import { SLACK_REPLACEMENT_TAMPLATE_NDS, SLACK_TEMPLATE_NDS_RESULT } from '../constants/slack_template.js';
import { TEMPLATE_EMERGENCY_NDS_READY, TEMPLATE_NDS_READY, TEMPLATE_REGULARLY_NDS_READY } from '../constants/template.js';
import { TOPIC_SEND_EMAIL, TOPIC_SEND_SLACK_MESSAGE, TOPIC_WRITE_SITE_RISK_HISTORY } from '../constants/topics.js';
import firestore from '../providers/firestore.js';
import { fetch } from '../services/nds.js';
import { createNdsSink } from '../services/sink.js';

const schema = {
  body: {
    type: 'object',
    required: ['id', 'fqdn', 'rank', 'result'],
    properties: {
      id: { type: 'integer' },
      fqdn: { type: 'string' },
      rank: { type: 'string' },
      result: { type: 'object' },
    },
  },
};

const pubSubClient = new PubSub({ projectId: process.env.PROJECT_ID });

const handler = async (request, reply) => {
  const { id, fqdn, rank, result, ...rest } = request.body;

  const snapshot = await firestore.collection(COLLECTION_NDS).where('scanId', '==', `${id}`).get();
  if (snapshot.empty) {
    console.error(`Nds record not found scanId: ${id}`);
    return reply.send('1');
  }
  if (snapshot.size > 1) {
    console.error(`Nds record found multiple scanId: ${id}`);
    return reply.send('1');
  }
  const doc = snapshot.docs[0];
  const code = doc.id;
  const data = doc.data();
  const lang = data?.lang || 'ja';

  let resolvedId = id;
  let resolvedFqdn = fqdn;
  let resolvedRank = rank;
  let resolvedResult = result;
  let resolvedRest = rest;

  if (lang !== 'ja') {
    const { id: fetchedId, fqdn: fetchedFqdn, rank: fetchedRank, result: fetchedResult, ...fetchedRest } = await fetch(id, lang);
    resolvedId = fetchedId;
    resolvedFqdn = fetchedFqdn;
    resolvedRank = fetchedRank;
    resolvedResult = fetchedResult;
    resolvedRest = fetchedRest;
  }
  const createdAt = data.createdAt ?? (new Date()).toISOString();
  await doc.ref.update({ createdAt, result: { rank: resolvedRank, result: resolvedResult, ...resolvedRest } });

  let isIncludedCve = false;
  if (data.cve) {
    isIncludedCve = resolvedResult.items.map(i => includesCve(i.metadata, data.cve)).reduce((pre, cur) => pre || cur, false);
  }

  const log = await createNdsSink({ email: data.email, fqdn: resolvedFqdn, createdAt, expiredAt: data.expiredAt, code, scanId: id, rank: resolvedRank, result: resolvedResult, ...resolvedRest, isRegularly: data.isRegularly ?? false, cve: data.cve, isIncludedCve, lang });

  let topic = pubSubClient.topic(TOPIC_WRITE_SITE_RISK_HISTORY);
  let message = Buffer.from(JSON.stringify({ email: data.email, fqdn: resolvedFqdn, result: { nds: log }, lang }));
  await topic.publishMessage({ data: message });

  if (data.threadTs) {
    topic = pubSubClient.topic(TOPIC_SEND_SLACK_MESSAGE);
    message = Buffer.from(JSON.stringify({ token: process.env.SECRET_SLACK_USER_TOKEN, channelId: process.env.SLACK_FQDN_CHANNEL_ID, message: { template: SLACK_TEMPLATE_NDS_RESULT, params: log }, threadTs: data.threadTs ?? null }));
    await topic.publishMessage({ data: message });

    message = Buffer.from(JSON.stringify({ token: process.env.SECRET_SLACK_USER_TOKEN, channelId: process.env.SLACK_FQDN_CHANNEL_ID, threadTs: data.threadTs ?? null, updateOptions: [{ template: SLACK_REPLACEMENT_TAMPLATE_NDS, params: log }] }));
    await topic.publishMessage({ data: message });
  }

  topic = pubSubClient.topic(TOPIC_SEND_EMAIL);
  let url = `${process.env.HOST}${process.env.PATH_PREFIX}/check/site-risk/?code=${code}`;

  let template = TEMPLATE_NDS_READY;
  let cveDetail;
  if (data.cve) {
    template = TEMPLATE_EMERGENCY_NDS_READY;
    url += `&cve=${data.cve}`;
    cveDetail = CVE_NAMES[data.cve];
    if (!isIncludedCve) url = undefined;
  }
  if (data.isRegularly) {
    template = TEMPLATE_REGULARLY_NDS_READY;
    url += `&is_regularly=1`;
  }

  message = Buffer.from(JSON.stringify({ email: data.email, template, params: { createdAt, expiredAt: data.expiredAt, fqdn: resolvedFqdn, url, cve: data.cve, cveDetail }, lang }));
  await topic.publishMessage({ data: message });

  return reply.send('1');
};

const includesCve = (item, cve) => {
  if (item === null) return false;
  if (typeof item !== 'object' || !Array.isArray(item.cves)) return false;
  return item.cves.some(item => item.cve === cve);
};

export default { schema, handler };
